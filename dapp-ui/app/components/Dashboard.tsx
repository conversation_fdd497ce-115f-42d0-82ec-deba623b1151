'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  CurrencyDollarIcon,
  PaperAirplaneIcon,
  ClockIcon,
  ChartBarIcon,
  CogIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { useWeb3 } from '../contexts/Web3Context';
import { config, formatTokenAmount } from '../config';
import { Card, Button, LoadingSpinner } from './ui';
import { TokenStats, Transaction } from '../types';

const Dashboard: React.FC = () => {
  const { wallet, tokenBalance, isLoading } = useWeb3();
  const [tokenStats, setTokenStats] = useState<TokenStats | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);
  const [statsLoading, setStatsLoading] = useState(false);

  // Fetch token stats
  useEffect(() => {
    const fetchTokenStats = async () => {
      if (!wallet.isConnected) return;
      
      setStatsLoading(true);
      try {
        const response = await fetch(`${config.API_BASE_URL}/token/stats`);
        if (response.ok) {
          const data = await response.json();
          setTokenStats(data.data);
        }
      } catch (error) {
        console.error('Failed to fetch token stats:', error);
      } finally {
        setStatsLoading(false);
      }
    };

    fetchTokenStats();
  }, [wallet.isConnected]);

  // Fetch recent transactions
  useEffect(() => {
    const fetchRecentTransactions = async () => {
      if (!wallet.isConnected || !wallet.address) return;
      
      try {
        const response = await fetch(
          `${config.API_BASE_URL}/token/transactions/${wallet.address}?limit=5`
        );
        if (response.ok) {
          const data = await response.json();
          setRecentTransactions(data.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch transactions:', error);
      }
    };

    fetchRecentTransactions();
  }, [wallet.isConnected, wallet.address]);

  if (!wallet.isConnected) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Card className="max-w-md w-full text-center">
          <div className="space-y-4">
            <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Welcome to DATACOIN dApp
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Connect your wallet to start managing your DATACOIN tokens
            </p>
          </div>
        </Card>
      </div>
    );
  }

  if (!wallet.isCorrectNetwork) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Card className="max-w-md w-full text-center">
          <div className="space-y-4">
            <div className="bg-yellow-100 dark:bg-yellow-900/20 rounded-full p-3 w-fit mx-auto">
              <CogIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Wrong Network
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Please switch to the correct network to continue
            </p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="mt-1 text-gray-600 dark:text-gray-400">
            Manage your DATACOIN tokens and view your portfolio
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Token Balance */}
          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Your Balance
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatTokenAmount(tokenBalance.formattedBalance)} {config.TOKEN_SYMBOL}
                </p>
              </div>
            </div>
          </Card>

          {/* Total Supply */}
          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Supply
                </p>
                {statsLoading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {tokenStats ? formatTokenAmount(tokenStats.totalSupply) : '0'} {config.TOKEN_SYMBOL}
                  </p>
                )}
              </div>
            </div>
          </Card>

          {/* Token Price */}
          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowUpIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Token Price
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${tokenStats?.price.toFixed(2) || '1.00'}
                </p>
              </div>
            </div>
          </Card>

          {/* Market Cap */}
          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Market Cap
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${tokenStats ? formatTokenAmount(tokenStats.marketCap.toString()) : '0'}
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Quick Actions */}
          <Card title="Quick Actions">
            <div className="grid grid-cols-2 gap-4">
              <Link href="/transfer">
                <Button className="w-full flex items-center justify-center space-x-2">
                  <PaperAirplaneIcon className="h-5 w-5" />
                  <span>Send Tokens</span>
                </Button>
              </Link>
              
              <Link href="/purchase">
                <Button variant="secondary" className="w-full flex items-center justify-center space-x-2">
                  <CurrencyDollarIcon className="h-5 w-5" />
                  <span>Buy Tokens</span>
                </Button>
              </Link>
              
              <Link href="/history">
                <Button variant="secondary" className="w-full flex items-center justify-center space-x-2">
                  <ClockIcon className="h-5 w-5" />
                  <span>History</span>
                </Button>
              </Link>
              
              {/* Admin Panel - only show if user is admin */}
              <Link href="/admin">
                <Button variant="secondary" className="w-full flex items-center justify-center space-x-2">
                  <CogIcon className="h-5 w-5" />
                  <span>Admin</span>
                </Button>
              </Link>
            </div>
          </Card>

          {/* Token Information */}
          <Card title="Token Information">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Name:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {config.TOKEN_NAME}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Symbol:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {config.TOKEN_SYMBOL}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Decimals:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {config.TOKEN_DECIMALS}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Contract:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white font-mono">
                  {config.CONTRACT_ADDRESS.slice(0, 10)}...{config.CONTRACT_ADDRESS.slice(-8)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Network:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Ethermint (Chain ID: {config.CHAIN_ID})
                </span>
              </div>
            </div>
          </Card>
        </div>

        {/* Recent Transactions */}
        <Card title="Recent Transactions">
          {recentTransactions.length === 0 ? (
            <div className="text-center py-8">
              <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No transactions yet
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Your transaction history will appear here
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentTransactions.map((tx) => (
                <div
                  key={tx.hash}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      tx.type === 'send' ? 'bg-red-100 dark:bg-red-900/20' :
                      tx.type === 'receive' ? 'bg-green-100 dark:bg-green-900/20' :
                      'bg-blue-100 dark:bg-blue-900/20'
                    }`}>
                      {tx.type === 'send' ? (
                        <ArrowUpIcon className="h-4 w-4 text-red-600 dark:text-red-400" />
                      ) : tx.type === 'receive' ? (
                        <ArrowDownIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                      ) : (
                        <CurrencyDollarIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                        {tx.type}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(tx.timestamp * 1000).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {tx.formattedValue} {config.TOKEN_SYMBOL}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {tx.status}
                    </p>
                  </div>
                </div>
              ))}
              
              <div className="text-center pt-4">
                <Link href="/history">
                  <Button variant="secondary" size="sm">
                    View All Transactions
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
