{"name": "datacoin-token", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "compile": "npx hardhat compile", "deploy": "npx hardhat run scripts/deploy-dtcerc.js", "deploy:local": "npx hardhat run scripts/deploy-dtcerc.js --network localhost", "mint": "npx hardhat run scripts/mint-tokens.js", "mint:local": "npx hardhat run scripts/mint-tokens.js --network localhost", "verify": "npx hardhat run scripts/verify-contract.js", "verify:local": "npx hardhat run scripts/verify-contract.js --network localhost", "node": "npx hardhat node"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.1.0", "dotenv": "^17.2.0", "hardhat": "^2.26.1"}, "dependencies": {"@openzeppelin/contracts": "^5.4.0"}}