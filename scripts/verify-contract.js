async function main() {
  console.log("🔍 Verifying DTCERC Contract");
  console.log("=" .repeat(40));
  
  const [deployer] = await ethers.getSigners();
  
  // Contract address - update this with your deployed contract address
  const CONTRACT_ADDRESS = "******************************************"; // Update this!
  
  console.log("📋 Verification Details:");
  console.log("  Verifier address:", deployer.address);
  console.log("  Contract address:", CONTRACT_ADDRESS);
  
  try {
    // Connect to the deployed contract
    const DATACOIN = await ethers.getContractFactory("DATACOIN");
    const token = await DATACOIN.attach(CONTRACT_ADDRESS);
    
    // Get network info
    const network = await ethers.provider.getNetwork();
    
    console.log("\n📊 Contract Information:");
    console.log("  Network:", network.name, "(Chain ID:", network.chainId.toString() + ")");
    console.log("  Contract Address:", CONTRACT_ADDRESS);
    
    // Token details
    const name = await token.name();
    const symbol = await token.symbol();
    const decimals = await token.decimals();
    const totalSupply = await token.totalSupply();
    const owner = await token.owner();
    
    console.log("\n🪙 Token Details:");
    console.log("  Name:", name);
    console.log("  Symbol:", symbol);
    console.log("  Decimals:", decimals.toString());
    console.log("  Total Supply:", ethers.formatEther(totalSupply), symbol);
    console.log("  Owner:", owner);
    
    // Check deployer balance
    const deployerBalance = await token.balanceOf(deployer.address);
    console.log("  Your Balance:", ethers.formatEther(deployerBalance), symbol);
    
    // Verify contract functions
    console.log("\n🔧 Contract Functions:");
    console.log("  ✅ name() - Working");
    console.log("  ✅ symbol() - Working");
    console.log("  ✅ decimals() - Working");
    console.log("  ✅ totalSupply() - Working");
    console.log("  ✅ balanceOf() - Working");
    console.log("  ✅ owner() - Working");
    
    // Check if deployer is owner
    const isOwner = owner.toLowerCase() === deployer.address.toLowerCase();
    console.log("  ✅ Owner check:", isOwner ? "You are the owner" : "You are NOT the owner");
    
    // Test allowance function
    try {
      const allowance = await token.allowance(deployer.address, deployer.address);
      console.log("  ✅ allowance() - Working");
    } catch (error) {
      console.log("  ❌ allowance() - Error:", error.message);
    }
    
    console.log("\n📱 MetaMask Integration:");
    console.log("  To add this token to MetaMask:");
    console.log("  1. Open MetaMask");
    console.log("  2. Go to 'Import tokens'");
    console.log("  3. Enter these details:");
    console.log(`     - Contract Address: ${CONTRACT_ADDRESS}`);
    console.log(`     - Token Symbol: ${symbol}`);
    console.log(`     - Decimals: ${decimals}`);
    
    console.log("\n🌐 Frontend Integration:");
    console.log("  Update your .env.local file:");
    console.log(`  NEXT_PUBLIC_CONTRACT_ADDRESS=${CONTRACT_ADDRESS}`);
    console.log(`  NEXT_PUBLIC_TOKEN_NAME=${name}`);
    console.log(`  NEXT_PUBLIC_TOKEN_SYMBOL=${symbol}`);
    
    if (totalSupply === 0n) {
      console.log("\n⚠️  Warning: Total supply is 0");
      console.log("  Run the mint script to create some tokens:");
      console.log("  npx hardhat run scripts/mint-tokens.js --network <your-network>");
    }
    
    console.log("\n✅ Contract verification completed successfully!");
    
  } catch (error) {
    console.error("\n❌ Contract verification failed:");
    console.error("Error:", error.message);
    console.log("\nPossible issues:");
    console.log("1. Contract address is incorrect");
    console.log("2. Contract is not deployed on this network");
    console.log("3. Network connection issues");
    console.log("4. Contract ABI mismatch");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("\n❌ Script failed:");
    console.error(error);
    process.exitCode = 1;
  });
